<?php

namespace App\Model\Order;

use App\Model\Permission\User;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * Class order_item_assignments
 * @property integer $id  
 * @property integer $order_id  订单id
 * @property integer $order_item_id  订单商品id
 * @property integer $assigned_user_id  分配用户id
 * @property string $system_order_no  系统单号
 * @property string $created_at  
 * @property string $updated_at  
 * @property string $deleted_at  
 */
class OrderItemAssignment extends MineModel
{
    protected ?string $table = 'order_item_assignments';

    protected array $fillable = ['id','order_id','order_item_id','assigned_user_id','system_order_no','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'integer','order_id' => 'integer','order_item_id' => 'integer','assigned_user_id' => 'integer','system_order_no' => 'string','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    /**
     * 关联订单商品
     */
    public function orderItem()
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id', 'id');
    }

    /**
     * 关联分配用户
     */
    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_user_id', 'id');
    }
}
