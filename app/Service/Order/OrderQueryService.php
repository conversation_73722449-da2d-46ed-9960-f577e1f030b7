<?php

namespace App\Service\Order;

use App\Constants\OrderConst;
use App\Constants\OrderTagConst;
use App\Http\Admin\Request\Order\OrderListRequest;
use App\Model\Permission\User;
use App\Repository\Order\OrderItemAssignmentRepository;
use App\Repository\Order\OrderItemRepository;
use App\Repository\Order\OrderRepository;
use App\Service\IService;
use Hyperf\Database\Model\Builder;
use Hyperf\DbConnection\Db;

class OrderQueryService extends IService
{

    public function __construct(
        protected readonly OrderRepository $repository,
        protected readonly OrderItemRepository $orderItemRepository,
        protected readonly OrderItemAssignmentRepository $orderItemAssignmentRepository,
    )
    {

    }

     public function list(User $user, OrderListRequest $request)
    {
        $shopIds = $this->getShopIds($user);
        $query = $this->orderItemRepository->getQuery();
        $queryParams = [];
        // 列表类型：1待发货，2已发货，3代发订单，4采购订单，5锁订单
        switch ($request->list_type) {
            case 1: // 待发货
            default:
                $query->whereIn('order_status', [OrderConst::ORDER_STATUS_PAYMENT, OrderConst::ORDER_STATUS_PART_DELIVERED]);
                $query->where('assignee_id', 0);
                $query->where('pick_task_id', 0);
                break;
            case 2: // 已发货
                $query->whereIn('order_status', [OrderConst::ORDER_STATUS_DELIVERED]);
                $query->where('assignee_id', 0);
                $query->where('pick_task_id', 0);
                break;
            case 3: // 代发订单
                $query->where('assignee_id', '>',0);
                break;
            case 4: // 采购订单
                $query->where('pick_task_id', '>',0);
                break;
            case 5: // 锁订单
                $query->whereNotNull('orders.locked_at');
                break;
        }

        $query->where($queryParams);
        $with = ['productSkus.product:id,name,product_no', 'order','orderCipherInfo'];
        return $this->queryList($query, $shopIds, $request, $with);
    }

    public function takeList(User $user, OrderListRequest $request): array
    {
        $shopIds = $this->getShopIds($user);
        $queryParams = [];
        $queryParams['shop_ids'] = $shopIds;
        $queryParams['order_by'] = 'order_items.id';
        $with = ['productSourceSkus.product:id,name,product_no', 'order', 'orderCipherInfo','goodsSku:id,goods_id,sku_id,sku_value'];
        $query = $this->orderItemRepository->getQuery()
            ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id');
        $list = $this->orderItemRepository->perQuery($query, $queryParams)->get();

        $orderIdArr = $list->pluck('id')->toArray();
        $lastQueryParams = [
            'order_items.id' => $orderIdArr,
            'pick_task_id' => 0,
        ];
        $pageWith = $this->orderItemRepository->pageWith($lastQueryParams, $with, $request->page, $request->page_size);
        return $pageWith;
    }

    /**
     * 根据店铺ID和订单号查询订单数据
     * @param int $shopId
     * @param string $tid
     * @return mixed
     */
    public function getByShopIdAndTid(int $shopId, string $tid)
    {
        $with = ['items','orderCipherInfo'];

        return $this->repository->getQuery()
            ->where('shop_id', $shopId)
            ->where('tid', $tid)
            ->with($with)
            ->first();
    }

    /**
     * @param array $queryParams
     * @param array $shopIds
     * @param OrderListRequest $request
     * @param array $with
     * @return array
     */
    public function queryList(Builder $query, array $shopIds, OrderListRequest $request, array $with): array
    {

        if ($request->list_type == 1){ //待发货
            $assignmentQuery = $this->orderItemRepository->getQuery()
                ->leftJoin('order_item_assignments', 'order_item_assignments.order_item_id', '=', 'order_items.id')
                ->whereIn('order_item_assignments.assigned_user_id', $shopIds)
                ->select([
                    Db::raw('group_concat(order_items.id,",") as ids'),
                    Db::raw('COALESCE(order_item_assignments.system_order_no, order_items.system_order_no) AS system_order_no')
                    ]);
            $query->union($assignmentQuery);
        }

        // 直接查询所有数据，不分页
        $query->whereIn('shop_id', $shopIds);
        $query->selectRaw('group_concat(id,",") as ids');
        $query->groupBy(['system_order_no']);
        $query->orderBy('id', 'desc');

        $list = $query->forPage($request->page, $request->page_size)->get();

        $itemIdArr = $list->pluck('ids')->map(function ($item) {
            return explode(',', $item);
        })->flatten()->unique()->values()->toArray();

        $finallyQuery = $this->orderItemRepository->getQuery();
        $finallyQuery->whereIn('id', $itemIdArr);
        $finallyQuery->with($with);
        $finallyList = $finallyQuery->get();
        
        $receiverMd5Arr = $finallyList->pluck('receiver_md5')->unique()->values()->toArray();
        // 查询具有相同 receiver_md5 的其他订单项
        $receiverMd5List = $this->orderItemRepository->getQuery()
            ->whereIn('order_items.shop_id', $shopIds)
            ->whereIn('order_items.order_status', [OrderConst::ORDER_STATUS_PAYMENT])
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.receiver_md5', $receiverMd5Arr)
            ->whereIn('order_items.id', $itemIdArr)
            ->orderBy('order_items.created_at', 'desc')
            ->select('order_items.*')
            ->get();

        //
        $finallyList->map(function ($item){

        });
        // 按 system_order_no 分组并构造返回结构
        $groupedItems = $finallyList->groupBy('system_order_no')->map(function ($items, $system_order_no) use ($receiverMd5List) {
            $first = $items->first();
            $count = $items->count();
            $tags = [];
            if ($count > 1){
                $tags[OrderTagConst::IS_MERGE] = 1;
            }
            if ($first->split_merge_flag == OrderConst::SPLIT_MERGE_FLAG_SPLIT){
                $tags[OrderTagConst::IS_SPLIT] = 1;
            }
            $receiver_md5 = $first->order->receiver_md5 ?? '';
            $receiverMd5Count = $receiverMd5List->where('receiver_md5', $receiver_md5)->count();
            if ($receiverMd5Count > 1){
                $tags[OrderTagConst::IS_MERGEABLE] = 1;
            }
            return [
                'system_order_no' => $system_order_no,
                'tags' => $tags,
                'receiver_md5' => $receiver_md5,
                'itemCount' => $count,
                'items' => $items->toArray(),
            ];
        })->values()->toArray();

        // 使用 array_multisort 根据 $list 的排序重新排序
        $orderMap = array_flip(array_column($list->toArray(), 'system_order_no'));
        // 2. 为 arr2 生成一个临时的顺序数组
        $sortOrder = [];
        foreach ($groupedItems as $item) {
            // 同样，未找到的元素给予一个很大的值
            $sortOrder[] = $orderMap[$item['system_order_no']] ?? PHP_INT_MAX;
        }
        // 3. 使用 array_multisort 进行排序
        array_multisort($sortOrder, SORT_ASC, $groupedItems);
        $groupedItems = collect($groupedItems);

        return [
            'total' => $groupedItems->count(),
            'list' => $groupedItems,
        ];
    }
    
    /**
     * 检查订单项是否可以合并
     * @param User $user
     * @param array $orderItemIds
     * @return array
     */
    public function checkMergeable(User $user, array $orderItemIds): array
    {
        $shopIds = $this->getShopIds($user);
        
        // 查询订单项并关联订单信息
        $orderItems = $this->orderItemRepository->getQuery()
            ->whereIn('order_items.id', $orderItemIds)
            ->whereIn('order_items.shop_id', $shopIds)
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->select('order_items.id', 'orders.receiver_md5')
            ->get();
        
        // 按 receiver_md5 分组
        $groupedItems = $orderItems->groupBy('receiver_md5')->map(function ($items) {
            return [
                'receiver_md5' => $items->first()->receiver_md5,
                'itemCount' => $items->count(),
                'items' => $items->pluck('id')->toArray()
            ];
        })->filter(function ($group) {
            // 过滤掉数量小于1的组
            return $group['itemCount'] >= 1;
        })->values();
        
        return $groupedItems->toArray();
    }
    
    /**
     * 查询可合并的订单列表
     * @param User $user 用户
     * @param int $orderItemId 订单项ID
     * @return array
     */
    public function getMergeableOrderItemList(User $user, int $orderItemId): array
    {
        $shopIds = $this->getShopIds($user);
        
        // 先获取指定订单项的 receiver_md5
        $orderItem = $this->orderItemRepository->getQuery()
            ->where('order_items.id', $orderItemId)
            ->whereIn('order_items.shop_id', $shopIds)
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->select('order_items.id', 'orders.receiver_md5', 'order_items.shop_id')
            ->first();
            
        if (!$orderItem) {
            return [];
        }
        
        // 查询具有相同 receiver_md5 的其他订单项
        $mergeableOrderItems = $this->orderItemRepository->getQuery()
            ->where('order_items.shop_id', $orderItem->shop_id)
            ->whereIn('order_items.order_status', [OrderConst::ORDER_STATUS_PAYMENT])
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.receiver_md5', $orderItem->receiver_md5)
            ->orderBy('order_items.created_at', 'desc')
            ->select('order_items.*')
            ->get()
            ->toArray();

        return [
            'list' => $mergeableOrderItems,
        ];
    }

}