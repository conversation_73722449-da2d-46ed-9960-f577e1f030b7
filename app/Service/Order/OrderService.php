<?php

namespace App\Service\Order;

use App\Constants\OrderConst;
use App\Exception\BusinessException;
use App\Http\Admin\Request\Order\OrderFreeItem;
use App\Http\Admin\Request\Order\OrderFreeRequest;
use App\Http\Admin\Request\Order\OrderItemUnassignSupplierRequest;
use App\Http\Admin\Request\Order\OrderItemUpdateProductBatchRequest;
use App\Http\Admin\Request\Order\OrderItemUpdateProductRequest;
use App\Http\Admin\Request\Order\OrderListRequest;
use App\Http\Admin\Request\Order\OrderRequest;
use App\Http\Admin\Request\Order\OrderItemAssignSupplierRequest;
use App\Http\Common\ResultCode;
use App\Model\Permission\User;
use App\Model\Shop\Shop;
use App\Repository\Order\OrderRepository;
use App\Repository\Order\OrderItemRepository;
use App\Repository\Order\OrderItemAssignmentRepository;
use App\Service\IService;
use App\Utils\CommonUtil;
use Hyperf\DbConnection\Db;
use Carbon\Carbon;
use App\Model\Order\OrderItem;
use App\Model\Order\OrderItemAssignment;

class OrderService extends IService
{
    public function __construct(
        protected readonly OrderRepository     $repository,
        protected readonly OrderItemRepository $orderItemRepository,
        protected readonly OrderItemAssignmentRepository $orderItemAssignmentRepository,
    )
    {

    }

    public function getRepository(): OrderRepository
    {
        return $this->repository;
    }

    /**
     * 批量分配厂家
     * @param User $user 当前用户
     * @param OrderItemAssignSupplierRequest $request 请求参数
     * @return bool
     */
    public function assignSupplierBatch(User $user, OrderItemAssignSupplierRequest $request): bool
    {
        // 获取用户拥有的店铺ID列表
        $shopIds = $this->getShopIds($user);
        
        if (empty($shopIds)) {
            return false;
        }
        
        // 查找需要更新的订单项，并确保这些订单项属于用户的店铺
        $orderItems = OrderItem::whereIn('id', $request->order_item_id_arr)
            ->whereIn('shop_id', $shopIds)
            ->with('order:id,tid')
            ->get();
        $orderShopIds = $orderItems->pluck('shop_id')->unique()->values()->toArray();
        // 检查$orderShopIds 店铺是否过期
        $shopNameStr = Shop::whereIn('id', $orderShopIds)
            ->where('end_at', '<', Carbon::now())
            ->pluck('shop_name')
            ->implode(',');
        // 检查$orderShopIds 店铺是否过期
        if (!empty($shopNameStr)) {
            throw new BusinessException(ResultCode::NOT_FOUND, '店铺已过期:'.$shopNameStr);
        }

        // 如果没有找到匹配的订单项，则返回false
        if ($orderItems->isEmpty()) {
            throw new BusinessException(ResultCode::NOT_FOUND, '订单不存在或者不属于你');
        }

        // 使用事务处理分配操作
        Db::transaction(function () use ($orderItems, $request) {
            foreach ($orderItems as $orderItem) {
                // 先删除已存在的分配记录（如果有的话）
                $exists = OrderItemAssignment::where('order_item_id', $orderItem->id)->exists();
                if ($exists){
                    return;
                }

                // 创建新的分配记录
                OrderItemAssignment::create([
                    'order_id' => $orderItem->order_id,
                    'order_item_id' => $orderItem->id,
                    'assigned_user_id' => $request->supplier_id,
                    'system_order_no' => $orderItem->system_order_no,
                ]);
            }
        });

        return 1;
    }

    /**
     * 批量取消分配厂家
     * @param User $user 当前用户
     * @param OrderItemUnassignSupplierRequest $request 请求参数
     * @return bool
     */
    public function unassignSupplierBatch(User $user, OrderItemUnassignSupplierRequest $request): bool
    {
        // 获取用户拥有的店铺ID列表
        $shopIds = $this->getShopIds($user);
        
        if (empty($shopIds)) {
            return false;
        }
        
        // 查找需要更新的订单项，并确保这些订单项属于用户的店铺
        $orderItems = OrderItem::whereIn('id', $request->order_item_id_arr)
            ->whereIn('shop_id', $shopIds)
            ->get();

        // 如果没有找到匹配的订单项，则返回false
        if ($orderItems->isEmpty()) {
            throw new BusinessException(ResultCode::NOT_FOUND, '订单不存在或者不属于你');
        }

        // 删除相关的分配记录
        OrderItemAssignment::whereIn('order_item_id', $orderItems->pluck('id'))->delete();

        return 1;
    }

    /**
     * 创建自由打印订单
     * @param OrderFreeRequest $request
     * @param User $user
     * @return mixed
     * @throws \Throwable
     */
    public function createFree(OrderFreeRequest $request, User $user): mixed
    {
        return Db::transaction(function () use ($request, $user) {
            // 生成订单号
            $tid = $this->generateOrderNo();
            $shop = $user->shop()->first();

            // 先组装所有 item 数据
            $itemsData = [];
            $totalAmount = 0;
            $totalSkuNum = 0;

            if (!empty($request->items)) {
                foreach ($request->items as $index => $item) {
                    $itemTotal = bcmul($item->sku_price, $item->sku_num, 2);
                    $totalAmount = bcadd($totalAmount, $itemTotal, 2);
                    $totalSkuNum += $item->sku_num;

                    $itemsData[] = $this->prepareOrderItemData($item, $tid, $shop, $index, $itemTotal);
                }
            }

            // 准备订单数据，使用计算出的金钱相关字段
            $orderData = $this->prepareOrderData($request, $tid, $shop, $totalAmount, $totalSkuNum, count($request->items ?? []));

            // 创建订单
            $order = $this->repository->create($orderData);

            // 批量插入订单商品，补充 order_id
            foreach ($itemsData as $itemData) {
                $itemData['order_id'] = $order->id;
                $this->orderItemRepository->create($itemData);
            }

            return $order;
        });
    }

    /**
     * 批量更新订单项的产品信息
     *
     * @param User $user
     * @param OrderItemUpdateProductBatchRequest $request
     * @return bool
     */
    public function updateOrderItemProductBatch(User $user, OrderItemUpdateProductBatchRequest $request): bool
    {
        $list = $request->list;
        $shopIds = $this->getShopIds($user);

        Db::transaction(function () use ($user, $list, $shopIds) {
            foreach ($list as $item) {
                $orderItem = $this->orderItemRepository->findById($item['order_item_id']);

                if (!$orderItem) {
                    throw new BusinessException(ResultCode::NOT_FOUND, '订单项不存在');
                }

                // 判断订单是否属于当前用户的店铺
                if (!in_array($orderItem->shop_id, $shopIds) && $orderItem->assignee_id != $user->id) {
                    throw new BusinessException(ResultCode::BAD_REQUEST, '订单项不属于当前用户');
                }

                $this->orderItemRepository->updateById($item['order_item_id'], [
                    'product_id' => $item['product_id'],
                    'product_sku_id' => $item['product_sku_id'],
                    'pick_price' => $item['pick_price'], // 更新拿货价
                ]);
            }
        });

        
        return true;
    }

    /**
     * 批量拆单功能
     *
     * @param array $list 包含多个itemIdArr的数组
     * @return bool
     */
    public function splitOrderItems(User $user,array $list): bool
    {
        $shopIds = $this->getShopIds($user);
        // 收集所有需要拆分的订单项ID
        $allItemIdArr = [];
        foreach ($list as $item) {
            $allItemIdArr = array_merge($allItemIdArr, $item['itemIdArr']);
        }
        
        // 去重
        $allItemIdArr = array_unique($allItemIdArr);
        
        // 一次查询所有需要拆分的订单项
        $orderItems = $this->orderItemRepository->getQuery()
            ->whereIn('shop_id', $shopIds)
            ->whereIn('id', $allItemIdArr)
            ->get();

        if ($orderItems->isEmpty()) {
            throw new BusinessException(ResultCode::NOT_FOUND, '未找到指定的订单项');
        }

        Db::transaction(function () use ($list, $orderItems) {
            foreach ($list as $item) {
                $itemIdArr = $item['itemIdArr'];
                
                // 从已查询的订单项中筛选出当前组的订单项
                $currentOrderItems = $orderItems->whereIn('id', $itemIdArr);
                
                if ($currentOrderItems->isEmpty()) {
                    continue;
                }
                
                // 为选中的订单项分配新的system_order_no
                $newSystemOrderNo = $this->generateSystemOrderNo();
                
                // 更新这些订单项的system_order_no
                $this->orderItemRepository->getQuery()
                    ->whereIn('id', $itemIdArr)
                    ->update(['system_order_no' => $newSystemOrderNo]);
                $this->orderItemRepository->updateByIds($itemIdArr, [
                    'system_order_no' => $newSystemOrderNo,
                    'split_merge_flag' => OrderConst::SPLIT_MERGE_FLAG_SPLIT,
                ]);
            }
        });

        return true;
    }

    /**
     * 批量合单功能
     *
     * @param User $user 当前用户
     * @param array $list 包含多个itemIdArr和targetOid的数组
     * @return bool
     */
    public function mergeOrderItems(User $user, array $list): bool
    {
        $shopIds = $this->getShopIds($user);
        
        // 收集所有需要合并的订单项ID
        $allItemIdArr = [];
        foreach ($list as $item) {
            $allItemIdArr = array_merge($allItemIdArr, $item['itemIdArr']);
        }
        
        // 去重
        $allItemIdArr = array_unique($allItemIdArr);
        
        // 一次查询所有需要合并的订单项
        $orderItems = $this->orderItemRepository->getQuery()
            ->whereIn('shop_id', $shopIds)
            ->whereIn('id', $allItemIdArr)
            ->get();

        if ($orderItems->isEmpty()) {
            throw new BusinessException(ResultCode::NOT_FOUND, '未找到指定的订单项');
        }

        Db::transaction(function () use ($list, $orderItems) {
            foreach ($list as $item) {
                $itemIdArr = $item['itemIdArr'];
                $targetOid = $item['targetItemId'];
                
                // 从已查询的订单项中筛选出当前组的订单项
                $currentOrderItems = $orderItems->whereIn('id', $itemIdArr);

                if ($currentOrderItems->isEmpty()) {
                    continue;
                }
                // 查询出$targetOrderItem
                $targetOrderItem = $orderItems->where('id', $targetOid)->first();
                if (!$targetOrderItem) {
                    throw new BusinessException(ResultCode::NOT_FOUND, '未找到目标订单项:'.$targetOid);
                }

                // 更新这些订单项的system_order_no为targetOid
                $this->orderItemRepository->updateByIds($itemIdArr, [
                    'system_order_no' => $targetOrderItem->system_order_no,
                    'split_merge_flag' => OrderConst::SPLIT_MERGE_FLAG_MERGE,
                ]);
            }
        });

        return true;
    }

    /**
     * 生成系统订单号
     * @return string
     */
    private function generateSystemOrderNo(): string
    {
        return 'SO' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 生成订单号
     * @return string
     */
    private function generateOrderNo(): string
    {
        return 'FR' . date('YmdHis') . mt_rand(1000, 9999);
    }


    /**
     * 准备订单数据（使用计算后的金额）
     * @param OrderFreeRequest $request
     * @param string $tid
     * @param Shop $shop
     * @param string $totalAmount
     * @param int $totalSkuNum
     * @param int $goodsTotalNum
     * @return array
     */
    private function prepareOrderData(OrderFreeRequest $request, string $tid, Shop $shop, string $totalAmount, int $totalSkuNum, int $goodsTotalNum): array
    {
        $now = Carbon::now();
        $orderData = [
            'tid' => $tid,
            'type' => 0, // 默认类型
            'order_type' => OrderConst::ORDER_TYPE_CUSTOMIZE, // 自由打印订单
            'order_status' => $request->order_status, // 待发货状态
            'print_status' => OrderConst::PRINT_STATUS_UNPRINTED, // 未打印
            'shop_id' => $shop->id,
            'receiver_province' => $request->receiver_province ?? '',
            'receiver_city' => $request->receiver_city ?? '',
            'receiver_district' => $request->receiver_district ?? '',
            'receiver_town' => $request->receiver_town ?? '',
            'receiver_name' => $request->receiver_name ?? '',
            'receiver_phone' => $request->receiver_phone ?? '',
            'receiver_address' => $request->receiver_address ?? '',
            'buyer_message' => $request->buyer_message ?? '',
            'seller_memo' => $request->seller_memo ?? '',
            'payment' => $totalAmount, // 使用计算出的总金额
            'total_fee' => $totalAmount, // 使用计算出的总金额
            'discount_fee' => 0.00,
            'post_fee' => 0.00,
            'goods_total_num' => $goodsTotalNum,
            'sku_num' => $totalSkuNum,
            'order_created_at' => $now,
            'order_updated_at' => $now,
        ];
        $orderData['receiver_md5'] = $this->genFreeReceiverMd5($orderData);
        return $orderData;
    }


    /**
     * 准备订单商品数据（不包含order_id）
     * @param OrderFreeItem $item
     * @param string $tid
     * @param Shop $shop
     * @param int $index
     * @param string $itemTotal
     * @return array
     */
    private function prepareOrderItemData(OrderFreeItem $item, string $tid, Shop $shop, int $index, string $itemTotal): array
    {
        $now = Carbon::now();

        return [
            'shop_id' => $shop->id,
            'tid' => $tid,
            'oid' => $tid . '_' . str_pad(($index + 1), 3, "0", STR_PAD_LEFT),
            'system_order_no' => $tid,
            'product_id' => $item->product_id ?? 0,
            'product_sku_id' => $item->product_sku_id ?? 0,
            'goods_id' => 0, // OrderFreeItem 中没有 goods_id 字段，设为默认值
            'goods_title' => $item->goods_title ?? '',
            'sku_num' => $item->sku_num,
            'sku_value' => $item->sku_value ?? '',
            'outer_sku_iid' => $item->outer_sku_iid ?? '',
            'weight' => $item->weight ?? 0,
            'volume' => $item->volume ?? 0,
            'num_iid' => $item->num_iid ?? '',
            'sku_id' => $item->sku_id ?? '',
            'order_status' => OrderConst::ORDER_STATUS_PAYMENT, // 待发货状态
            'print_status' => OrderConst::PRINT_STATUS_UNPRINTED, // 未打印
            'sku_price' => $item->sku_price,
            'payment' => $itemTotal,
            'total_fee' => $itemTotal,
            'discount_fee' => 0.00,
            'order_created_at' => $now,
            'order_updated_at' => $now,
        ];
    }

    private function genFreeReceiverMd5(array $order): string
    {
        $receiver_address = CommonUtil::filterZeroChar($order['receiver_address']);
        $addressArr = [
            $order['shop_id'],
            $order['free_order_no'],
            $order['receiver_state'],
            $order['receiver_city'],
            $order['receiver_district'],
            $receiver_address,
            $order['receiver_name'],
            $order['receiver_phone'],
        ];
        return md5(implode(',', $addressArr));
    }

}